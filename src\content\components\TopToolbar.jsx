import React, { useEffect } from "react";
import { FaBell, FaCog } from "react-icons/fa"; // Keep existing imports for bell and cog

const TopToolbar = () => {
  useEffect(() => {
    console.log("📌 TopToolbar mounted on WhatsApp Web");
  }, []);

  return (
    <div
      style={{
        height: '100%',
        width: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingLeft: '1rem',
        paddingRight: '1rem',
        boxSizing: 'border-box',
        backgroundColor: 'white',
      }}
    >
      {/* Left side: Buttons with Emojis */}
      <div style={{ display: 'flex', gap: '1rem', overflowX: 'auto', paddingBottom: '4px' }}> {/* Added overflow for many buttons */}
        {/* Original buttons */}        
        <button style={{ color: '#4a5568', padding: '4px 8px', borderRadius: '4px', transition: 'background-color 0.2s', fontSize: '0.875rem', whiteSpace: 'nowrap' }} className="hover:bg-gray-100">🟢 Inbox </button>
        <button style={{ color: '#4a5568', padding: '4px 8px', borderRadius: '4px', transition: 'background-color 0.2s', fontSize: '0.875rem', whiteSpace: 'nowrap' }} className="hover:bg-gray-100">⭐ Starred</button>
        <button style={{ color: '#4a5568', padding: '4px 8px', borderRadius: '4px', transition: 'background-color 0.2s', fontSize: '0.875rem', whiteSpace: 'nowrap' }} className="hover:bg-gray-100">🤎 Unread </button> {/* Using brown heart for unread/closed folder look */}
        <button style={{ color: '#4a5568', padding: '4px 8px', borderRadius: '4px', transition: 'background-color 0.2s', fontSize: '0.875rem', whiteSpace: 'nowrap' }} className="hover:bg-gray-100">🤎 Closed</button> {/* Using brown heart for unread/closed folder look */}
        <button style={{ color: '#4a5568', padding: '4px 8px', borderRadius: '4px', transition: 'background-color 0.2s', fontSize: '0.875rem', whiteSpace: 'nowrap' }} className="hover:bg-gray-100">⏰ Snoozed</button>
        <button style={{ color: '#4a5568', padding: '4px 8px', borderRadius: '4px', transition: 'background-color 0.2s', fontSize: '0.875rem', whiteSpace: 'nowrap' }} className="hover:bg-gray-100">🚩 Follow Up</button>
        <button style={{ color: '#4a5568', padding: '4px 8px', borderRadius: '4px', transition: 'background-color 0.2s', fontSize: '0.875rem', whiteSpace: 'nowrap' }} className="hover:bg-gray-100">🏆 VIP</button>
        
        {/* Plus button */}
        <button style={{ color: '#4a5568', padding: '4px 8px', borderRadius: '4px', transition: 'background-color 0.2s', fontSize: '0.875rem', fontWeight: 'bold' }} className="hover:bg-gray-100">+</button>
      </div>

      {/* Right side: App name and icons */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
        <div style={{ fontSize: '1.125rem', fontWeight: 'bold', color: '#10B981' }}>Whatsapofy</div>
        <button style={{ color: '#4a5568', transition: 'color 0.2s' }} className="hover:text-blue-600"><FaBell size={20} /></button>
        <button style={{ color: '#4a5568', transition: 'color 0.2s' }} className="hover:text-blue-600"><FaCog size={20} /></button>
      </div>
    </div>
  );
};

export default TopToolbar;