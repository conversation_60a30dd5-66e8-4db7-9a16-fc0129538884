import React from 'react'; // React is needed for JSX
import { createRoot } from "react-dom/client";
import InjectedSidebarButtons from "./components/InjectedSidebarButtons"; // Adjust path as needed
import TopToolbar from "./components/TopToolbar"; // Adjust path as needed
import ChatHeaderHover from "./components/ChatHeaderHover"; // Adjust path as needed
import "./App.css"; // Adjust path as needed, if App.css is relevant to this entry point

console.log("🚀 Whatsapofy content script loaded");

// --- Helper Function: waitForElement ---
// A utility to wait for a specific DOM element to be available before executing a callback.
// This is crucial for dynamic web apps like WhatsApp Web where elements load asynchronously.
function waitForElement(selector, callback) {
  const el = document.querySelector(selector);
  if (el) {
    console.log(`✅ Element found immediately for selector: "${selector}"`);
    callback(el);
    return;
  }

  console.log(`⏳ Waiting for element with selector: "${selector}"`);
  const observer = new MutationObserver((mutations, obs) => {
    const element = document.querySelector(selector);
    if (element) {
      obs.disconnect(); // Stop observing once the element is found
      console.log(`✅ Element found by observer for selector: "${selector}"`);
      callback(element);
    }
  });

  // Observe the entire document body for changes in its child list and subtree
  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });
}

// --- Constant for Toolbar Height ---
// Defining this constant helps maintain consistent sizing across related elements.
const TOOLBAR_HEIGHT = '48px';

// --- 1. Injection for Top Toolbar ---
// This section handles the injection of your TopToolbar component. It targets the main WhatsApp body container
// and prepends the toolbar, ensuring it becomes an integrated part of the layout.
function injectTopToolbarIntoWhatsAppBody() {
  // Prevent re-injection if the toolbar's root element already exists in the DOM.
  if (document.getElementById("whatsapp-top-toolbar-root")) {
    console.log("⚠️ Top Toolbar already injected. Skipping injection.");
    return;
  }

  // CRITICAL: This selector targets the main div that contains WhatsApp's entire body content.
  // Based on your input, this is 'div.x78zum5.xdt5ytf.x5yr21d'.
  // Verify this class name periodically, as WhatsApp often changes its internal class names.
  const whatsappMainBodyContainerSelector = 'div.x78zum5.xdt5ytf.x5yr21d';

  waitForElement(whatsappMainBodyContainerSelector, (whatsappMainBodyContainer) => {
    console.log("Attempting to inject Top Toolbar into WhatsApp main body container...");

    // Create the container div where your React TopToolbar component will be rendered.
    const toolbarContainer = document.createElement("div");
    toolbarContainer.id = "whatsapp-top-toolbar-root";

    // Apply necessary styles to the container. Note that 'position: fixed' and 'z-index'
    // are intentionally omitted here, as the toolbar will now be part of the document flow.
    Object.assign(toolbarContainer.style, {
      height: TOOLBAR_HEIGHT, // Explicit height to reserve space
      width: '100%',           // Ensure it spans the full width of its parent
      boxSizing: 'border-box', // Include padding and border in the element's total width/height
      backgroundColor: 'white', // Set background color for the toolbar itself
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)', // Add a subtle shadow
      borderBottom: '1px solid #e2e8f0' // Add a bottom border
    });

    // Prepend the toolbarContainer to the identified WhatsApp main body container.
    // This will insert your toolbar as the *first child* of that WhatsApp div,
    // naturally pushing down all other WhatsApp content within it.
    whatsappMainBodyContainer.prepend(toolbarContainer);
    console.log("✅ Top Toolbar container created and prepended into WhatsApp main body container.");

    // Create a React root and render your TopToolbar component into the newly created container.
    const root = createRoot(toolbarContainer);
    root.render(<TopToolbar />);
    console.log("✅ TopToolbar mounted as part of WhatsApp Web's flow.");

    // Since the toolbar is now inserted *inside* the main content container and prepended,
    // it will automatically push down the other content. Therefore, no additional
    // 'padding-top' adjustments are typically needed on the main WhatsApp content itself.
    console.log("ℹ️ No explicit padding adjustment needed for main content as toolbar is prepended internally.");
  });
}

// Execute the function to start the injection process for the top toolbar.
injectTopToolbarIntoWhatsAppBody();

// --- 2. Injection for Sidebar Buttons ---
// This section injects custom buttons into WhatsApp's left sidebar, positioned before a specific <hr> element for precise placement.
waitForElement('header[data-tab="2"] > div > div[style="flex-grow: 1;"]', (flexGrowParentDiv) => {
  // Prevent re-injection if the sidebar buttons root already exists.
  if (document.getElementById("whatsapp-leftbar-buttons-root")) {
    console.log("⚠️ Sidebar buttons already injected. Skipping injection.");
    return;
  }

  // CRITICAL: Verify this HR selector by inspecting your WhatsApp Web's HTML.
  // This is used to accurately place your sidebar buttons.
  const hrElement = flexGrowParentDiv.querySelector('hr.xjm9jq1'); // Placeholder: Replace 'xjm9jq1' if different

  // Create the container for the sidebar buttons.
  const container = document.createElement("div");
  container.id = "whatsapp-leftbar-buttons-root";
  Object.assign(container.style, {
    marginTop: "8px",
    marginBottom: "8px",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    gap: "4px",
  });

  // Insert the container before the identified HR element if found,
  // otherwise, append it to the parent div as a fallback.
  if (hrElement && hrElement.parentNode === flexGrowParentDiv) {
    flexGrowParentDiv.insertBefore(container, hrElement);
    console.log("✅ Injected sidebar buttons inserted before HR element.");
  } else {
    flexGrowParentDiv.appendChild(container);
    console.warn("⚠️ HR element for sidebar buttons not found or not in expected position. Appending to end.");
    console.warn("Please inspect WhatsApp Web HTML for the correct HR element selector for precise placement.");
  }

  // Create a React root and render the InjectedSidebarButtons component.
  const root = createRoot(container);
  root.render(<InjectedSidebarButtons />);
  console.log("✅ InjectedSidebarButtons mounted into left navbar.");
});

// --- 3. Injection for ChatHeaderHover ---

// This section injects a hover component for the chat header, allowing for additional functionality when hovering over chat headers.

waitForElement('body', (bodyElement) => {
  // Prevent re-injection if the ChatHeaderHover root already exists.
  if (document.getElementById("whasapofy-chat-hover-root")) {
    console.log("⚠️ ChatHeaderHover already injected. Skipping injection.");
    return;
  }

  // Create a simple container div for the ChatHeaderHover component.
  const chatHoverContainer = document.createElement("div");
  chatHoverContainer.id = "whasapofy-chat-hover-root";
  bodyElement.appendChild(chatHoverContainer); // Append to the body

  console.log("ChatHeaderHover container created and appended.");

  // Create a React root and render the ChatHeaderHover component.
  const root = createRoot(chatHoverContainer);
  root.render(<ChatHeaderHover />);
  console.log("✅ ChatHeaderHover mounted on WhatsApp Web.");
});